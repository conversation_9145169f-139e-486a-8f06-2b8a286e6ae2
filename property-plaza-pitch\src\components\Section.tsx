'use client';

import { motion } from 'framer-motion';
import { ReactNode } from 'react';

interface SectionProps {
  id: string;
  children: ReactNode;
  className?: string;
  background?: 'default' | 'gradient' | 'accent';
}

const backgroundVariants = {
  default: 'bg-brand-dark',
  gradient: 'bg-gradient-to-br from-brand-dark via-brand-dark to-brand-dark/90',
  accent: 'bg-gradient-to-br from-brand-dark to-brand-accent/10',
};

export default function Section({ id, children, className = '', background = 'default' }: SectionProps) {
  return (
    <section
      id={id}
      className={`section ${backgroundVariants[background]} ${className}`}
    >
      <motion.div
        initial={{ opacity: 0, y: 50 }}
        whileInView={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.8, ease: 'easeOut' }}
        viewport={{ once: true, amount: 0.3 }}
        className="container mx-auto px-6 max-w-6xl"
      >
        {children}
      </motion.div>
    </section>
  );
}
