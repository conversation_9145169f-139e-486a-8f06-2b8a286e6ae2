{"version": 3, "sources": [], "sections": [{"offset": {"line": 15, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/_PRIVATE/Property%20Plaza%20presentation/property-plaza-pitch/src/components/Navigation.tsx"], "sourcesContent": ["'use client';\n\nimport { useState, useEffect } from 'react';\n\ninterface NavigationProps {\n  currentSection: number;\n  totalSections: number;\n}\n\nexport default function Navigation({ currentSection, totalSections }: NavigationProps) {\n  const [isVisible, setIsVisible] = useState(false);\n\n  useEffect(() => {\n    const timer = setTimeout(() => setIsVisible(true), 1000);\n    return () => clearTimeout(timer);\n  }, []);\n\n  const scrollToSection = (sectionIndex: number) => {\n    const section = document.getElementById(`section-${sectionIndex}`);\n    if (section) {\n      section.scrollIntoView({ behavior: 'smooth' });\n    }\n  };\n\n  if (!isVisible) return null;\n\n  return (\n    <nav className=\"fixed top-6 right-6 z-50 bg-brand-dark/80 backdrop-blur-sm rounded-full px-4 py-2 border border-brand-accent/20\">\n      <div className=\"flex items-center space-x-2\">\n        <span className=\"text-sm text-brand-text/70\">\n          {currentSection} of {totalSections}\n        </span>\n        <div className=\"flex space-x-1\">\n          {Array.from({ length: totalSections }, (_, i) => (\n            <button\n              key={i}\n              onClick={() => scrollToSection(i + 1)}\n              className={`w-2 h-2 rounded-full transition-all duration-300 ${\n                i + 1 === currentSection\n                  ? 'bg-brand-accent scale-125'\n                  : 'bg-brand-text/30 hover:bg-brand-text/50'\n              }`}\n              aria-label={`Go to section ${i + 1}`}\n            />\n          ))}\n        </div>\n      </div>\n    </nav>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AAFA;;;AASe,SAAS,WAAW,EAAE,cAAc,EAAE,aAAa,EAAmB;IACnF,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE3C,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,QAAQ,WAAW,IAAM,aAAa,OAAO;QACnD,OAAO,IAAM,aAAa;IAC5B,GAAG,EAAE;IAEL,MAAM,kBAAkB,CAAC;QACvB,MAAM,UAAU,SAAS,cAAc,CAAC,CAAC,QAAQ,EAAE,cAAc;QACjE,IAAI,SAAS;YACX,QAAQ,cAAc,CAAC;gBAAE,UAAU;YAAS;QAC9C;IACF;IAEA,IAAI,CAAC,WAAW,OAAO;IAEvB,qBACE,8OAAC;QAAI,WAAU;kBACb,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAK,WAAU;;wBACb;wBAAe;wBAAK;;;;;;;8BAEvB,8OAAC;oBAAI,WAAU;8BACZ,MAAM,IAAI,CAAC;wBAAE,QAAQ;oBAAc,GAAG,CAAC,GAAG,kBACzC,8OAAC;4BAEC,SAAS,IAAM,gBAAgB,IAAI;4BACnC,WAAW,CAAC,iDAAiD,EAC3D,IAAI,MAAM,iBACN,8BACA,2CACJ;4BACF,cAAY,CAAC,cAAc,EAAE,IAAI,GAAG;2BAP/B;;;;;;;;;;;;;;;;;;;;;AAcnB", "debugId": null}}, {"offset": {"line": 91, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/_PRIVATE/Property%20Plaza%20presentation/property-plaza-pitch/src/components/Section.tsx"], "sourcesContent": ["'use client';\n\nimport { motion } from 'framer-motion';\nimport { ReactNode } from 'react';\n\ninterface SectionProps {\n  id: string;\n  children: ReactNode;\n  className?: string;\n  background?: 'default' | 'gradient' | 'accent';\n}\n\nconst backgroundVariants = {\n  default: 'bg-brand-dark',\n  gradient: 'bg-gradient-to-br from-brand-dark via-brand-dark to-brand-dark/90',\n  accent: 'bg-gradient-to-br from-brand-dark to-brand-accent/10',\n};\n\nexport default function Section({ id, children, className = '', background = 'default' }: SectionProps) {\n  return (\n    <section\n      id={id}\n      className={`section ${backgroundVariants[background]} ${className}`}\n    >\n      <motion.div\n        initial={{ opacity: 0, y: 50 }}\n        whileInView={{ opacity: 1, y: 0 }}\n        transition={{ duration: 0.8, ease: 'easeOut' }}\n        viewport={{ once: true, amount: 0.3 }}\n        className=\"container mx-auto px-6 max-w-6xl\"\n      >\n        {children}\n      </motion.div>\n    </section>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AAFA;;;AAYA,MAAM,qBAAqB;IACzB,SAAS;IACT,UAAU;IACV,QAAQ;AACV;AAEe,SAAS,QAAQ,EAAE,EAAE,EAAE,QAAQ,EAAE,YAAY,EAAE,EAAE,aAAa,SAAS,EAAgB;IACpG,qBACE,8OAAC;QACC,IAAI;QACJ,WAAW,CAAC,QAAQ,EAAE,kBAAkB,CAAC,WAAW,CAAC,CAAC,EAAE,WAAW;kBAEnE,cAAA,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;YACT,SAAS;gBAAE,SAAS;gBAAG,GAAG;YAAG;YAC7B,aAAa;gBAAE,SAAS;gBAAG,GAAG;YAAE;YAChC,YAAY;gBAAE,UAAU;gBAAK,MAAM;YAAU;YAC7C,UAAU;gBAAE,MAAM;gBAAM,QAAQ;YAAI;YACpC,WAAU;sBAET;;;;;;;;;;;AAIT", "debugId": null}}, {"offset": {"line": 144, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/_PRIVATE/Property%20Plaza%20presentation/property-plaza-pitch/src/components/PillarCard.tsx"], "sourcesContent": ["'use client';\n\nimport { motion } from 'framer-motion';\n\ninterface PillarCardProps {\n  icon: string;\n  title: string;\n  description: string;\n  index: number;\n}\n\nexport default function PillarCard({ icon, title, description, index }: PillarCardProps) {\n  return (\n    <motion.div\n      initial={{ opacity: 0, y: 50, scale: 0.9 }}\n      whileInView={{ opacity: 1, y: 0, scale: 1 }}\n      transition={{ \n        duration: 0.6, \n        delay: index * 0.15,\n        ease: 'easeOut'\n      }}\n      whileHover={{ \n        scale: 1.05,\n        y: -10,\n        transition: { duration: 0.3 }\n      }}\n      className=\"group relative bg-gradient-to-br from-brand-dark/80 to-brand-dark/40 border border-brand-accent/20 rounded-2xl p-8 hover:border-brand-accent/60 transition-all duration-500 backdrop-blur-sm\"\n    >\n      {/* Glow effect on hover */}\n      <div className=\"absolute inset-0 rounded-2xl bg-gradient-to-br from-brand-accent/10 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-500\" />\n      \n      <div className=\"relative z-10\">\n        {/* Icon */}\n        <motion.div\n          whileHover={{ rotate: 360 }}\n          transition={{ duration: 0.6 }}\n          className=\"text-6xl mb-6 text-center\"\n        >\n          {icon}\n        </motion.div>\n        \n        {/* Title */}\n        <h3 className=\"text-2xl font-bold mb-4 text-brand-accent text-center group-hover:text-brand-accent/90 transition-colors duration-300\">\n          {title}\n        </h3>\n        \n        {/* Description */}\n        <p className=\"text-brand-text/80 text-center leading-relaxed group-hover:text-brand-text transition-colors duration-300\">\n          {description}\n        </p>\n        \n        {/* Decorative line */}\n        <motion.div\n          initial={{ width: 0 }}\n          whileInView={{ width: '100%' }}\n          transition={{ duration: 0.8, delay: index * 0.15 + 0.3 }}\n          className=\"h-0.5 bg-gradient-to-r from-transparent via-brand-accent to-transparent mt-6\"\n        />\n      </div>\n    </motion.div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AAFA;;;AAWe,SAAS,WAAW,EAAE,IAAI,EAAE,KAAK,EAAE,WAAW,EAAE,KAAK,EAAmB;IACrF,qBACE,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;QACT,SAAS;YAAE,SAAS;YAAG,GAAG;YAAI,OAAO;QAAI;QACzC,aAAa;YAAE,SAAS;YAAG,GAAG;YAAG,OAAO;QAAE;QAC1C,YAAY;YACV,UAAU;YACV,OAAO,QAAQ;YACf,MAAM;QACR;QACA,YAAY;YACV,OAAO;YACP,GAAG,CAAC;YACJ,YAAY;gBAAE,UAAU;YAAI;QAC9B;QACA,WAAU;;0BAGV,8OAAC;gBAAI,WAAU;;;;;;0BAEf,8OAAC;gBAAI,WAAU;;kCAEb,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wBACT,YAAY;4BAAE,QAAQ;wBAAI;wBAC1B,YAAY;4BAAE,UAAU;wBAAI;wBAC5B,WAAU;kCAET;;;;;;kCAIH,8OAAC;wBAAG,WAAU;kCACX;;;;;;kCAIH,8OAAC;wBAAE,WAAU;kCACV;;;;;;kCAIH,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wBACT,SAAS;4BAAE,OAAO;wBAAE;wBACpB,aAAa;4BAAE,OAAO;wBAAO;wBAC7B,YAAY;4BAAE,UAAU;4BAAK,OAAO,QAAQ,OAAO;wBAAI;wBACvD,WAAU;;;;;;;;;;;;;;;;;;AAKpB", "debugId": null}}, {"offset": {"line": 254, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/_PRIVATE/Property%20Plaza%20presentation/property-plaza-pitch/src/components/BackgroundAnimation.tsx"], "sourcesContent": ["'use client';\n\nimport { motion } from 'framer-motion';\n\nexport default function BackgroundAnimation() {\n  return (\n    <div className=\"absolute inset-0 overflow-hidden pointer-events-none\">\n      {/* Floating particles */}\n      {Array.from({ length: 20 }, (_, i) => (\n        <motion.div\n          key={i}\n          className=\"absolute w-2 h-2 bg-brand-accent/20 rounded-full\"\n          initial={{\n            x: Math.random() * window.innerWidth,\n            y: Math.random() * window.innerHeight,\n          }}\n          animate={{\n            x: Math.random() * window.innerWidth,\n            y: Math.random() * window.innerHeight,\n          }}\n          transition={{\n            duration: Math.random() * 20 + 10,\n            repeat: Infinity,\n            repeatType: 'reverse',\n            ease: 'linear',\n          }}\n        />\n      ))}\n      \n      {/* Gradient orbs */}\n      <motion.div\n        className=\"absolute top-1/4 left-1/4 w-96 h-96 bg-gradient-to-r from-brand-accent/10 to-transparent rounded-full blur-3xl\"\n        animate={{\n          scale: [1, 1.2, 1],\n          opacity: [0.3, 0.6, 0.3],\n        }}\n        transition={{\n          duration: 8,\n          repeat: Infinity,\n          ease: 'easeInOut',\n        }}\n      />\n      \n      <motion.div\n        className=\"absolute bottom-1/4 right-1/4 w-80 h-80 bg-gradient-to-l from-brand-accent/15 to-transparent rounded-full blur-3xl\"\n        animate={{\n          scale: [1.2, 1, 1.2],\n          opacity: [0.4, 0.2, 0.4],\n        }}\n        transition={{\n          duration: 10,\n          repeat: Infinity,\n          ease: 'easeInOut',\n        }}\n      />\n      \n      {/* Subtle grid pattern */}\n      <div className=\"absolute inset-0 opacity-5\">\n        <div className=\"w-full h-full\" style={{\n          backgroundImage: `\n            linear-gradient(rgba(16, 185, 129, 0.1) 1px, transparent 1px),\n            linear-gradient(90deg, rgba(16, 185, 129, 0.1) 1px, transparent 1px)\n          `,\n          backgroundSize: '50px 50px'\n        }} />\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AAFA;;;AAIe,SAAS;IACtB,qBACE,8OAAC;QAAI,WAAU;;YAEZ,MAAM,IAAI,CAAC;gBAAE,QAAQ;YAAG,GAAG,CAAC,GAAG,kBAC9B,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oBAET,WAAU;oBACV,SAAS;wBACP,GAAG,KAAK,MAAM,KAAK,OAAO,UAAU;wBACpC,GAAG,KAAK,MAAM,KAAK,OAAO,WAAW;oBACvC;oBACA,SAAS;wBACP,GAAG,KAAK,MAAM,KAAK,OAAO,UAAU;wBACpC,GAAG,KAAK,MAAM,KAAK,OAAO,WAAW;oBACvC;oBACA,YAAY;wBACV,UAAU,KAAK,MAAM,KAAK,KAAK;wBAC/B,QAAQ;wBACR,YAAY;wBACZ,MAAM;oBACR;mBAfK;;;;;0BAoBT,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gBACT,WAAU;gBACV,SAAS;oBACP,OAAO;wBAAC;wBAAG;wBAAK;qBAAE;oBAClB,SAAS;wBAAC;wBAAK;wBAAK;qBAAI;gBAC1B;gBACA,YAAY;oBACV,UAAU;oBACV,QAAQ;oBACR,MAAM;gBACR;;;;;;0BAGF,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gBACT,WAAU;gBACV,SAAS;oBACP,OAAO;wBAAC;wBAAK;wBAAG;qBAAI;oBACpB,SAAS;wBAAC;wBAAK;wBAAK;qBAAI;gBAC1B;gBACA,YAAY;oBACV,UAAU;oBACV,QAAQ;oBACR,MAAM;gBACR;;;;;;0BAIF,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;oBAAgB,OAAO;wBACpC,iBAAiB,CAAC;;;UAGlB,CAAC;wBACD,gBAAgB;oBAClB;;;;;;;;;;;;;;;;;AAIR", "debugId": null}}, {"offset": {"line": 371, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/_PRIVATE/Property%20Plaza%20presentation/property-plaza-pitch/src/app/page.tsx"], "sourcesContent": ["'use client';\n\nimport { useState, useEffect } from 'react';\nimport { motion } from 'framer-motion';\nimport Navigation from '@/components/Navigation';\nimport Section from '@/components/Section';\nimport PillarCard from '@/components/PillarCard';\nimport BackgroundAnimation from '@/components/BackgroundAnimation';\n\nexport default function Home() {\n  const [currentSection, setCurrentSection] = useState(1);\n  const totalSections = 9;\n\n  useEffect(() => {\n    const handleScroll = () => {\n      const sections = document.querySelectorAll('.section');\n      const scrollPosition = window.scrollY + window.innerHeight / 2;\n\n      sections.forEach((section, index) => {\n        const element = section as HTMLElement;\n        const top = element.offsetTop;\n        const bottom = top + element.offsetHeight;\n\n        if (scrollPosition >= top && scrollPosition < bottom) {\n          setCurrentSection(index + 1);\n        }\n      });\n    };\n\n    window.addEventListener('scroll', handleScroll);\n    return () => window.removeEventListener('scroll', handleScroll);\n  }, []);\n\n  return (\n    <main className=\"relative\">\n      <Navigation currentSection={currentSection} totalSections={totalSections} />\n\n      {/* Section 1: Hero */}\n      <Section id=\"section-1\" background=\"gradient\">\n        <BackgroundAnimation />\n        <div className=\"text-center relative z-10\">\n          <motion.div\n            initial={{ opacity: 0, scale: 0.8 }}\n            animate={{ opacity: 1, scale: 1 }}\n            transition={{ duration: 1, ease: 'easeOut' }}\n            className=\"mb-8\"\n          >\n            <motion.h1\n              className=\"text-5xl md:text-7xl font-bold mb-6 bg-gradient-to-r from-brand-text to-brand-accent bg-clip-text text-transparent\"\n              initial={{ opacity: 0, y: 30 }}\n              animate={{ opacity: 1, y: 0 }}\n              transition={{ duration: 1.2, delay: 0.2 }}\n            >\n              Empower Property Decisions in Bali\n            </motion.h1>\n            <motion.p\n              className=\"text-xl md:text-2xl text-brand-text/80 mb-8 max-w-3xl mx-auto\"\n              initial={{ opacity: 0, y: 20 }}\n              animate={{ opacity: 1, y: 0 }}\n              transition={{ duration: 1, delay: 0.4 }}\n            >\n              Through transparency, knowledge, connection and trust.\n            </motion.p>\n          </motion.div>\n\n          <motion.button\n            initial={{ opacity: 0, y: 20 }}\n            animate={{ opacity: 1, y: 0 }}\n            transition={{ duration: 0.8, delay: 0.6 }}\n            whileHover={{ scale: 1.05 }}\n            whileTap={{ scale: 0.95 }}\n            onClick={() => document.getElementById('section-2')?.scrollIntoView({ behavior: 'smooth' })}\n            className=\"group relative bg-brand-accent hover:bg-brand-accent/90 text-white px-8 py-4 rounded-full text-lg font-semibold transition-all duration-300 transform hover:shadow-lg hover:shadow-brand-accent/25 overflow-hidden\"\n          >\n            <span className=\"relative z-10\">\n              Start the Experience\n              <motion.span\n                animate={{ y: [0, 5, 0] }}\n                transition={{ duration: 1.5, repeat: Infinity }}\n                className=\"ml-2 inline-block\"\n              >\n                ↓\n              </motion.span>\n            </span>\n            <motion.div\n              className=\"absolute inset-0 bg-gradient-to-r from-brand-accent to-brand-accent/80\"\n              whileHover={{ scale: 1.1 }}\n              transition={{ duration: 0.3 }}\n            />\n          </motion.button>\n        </div>\n      </Section>\n\n      {/* Section 2: The Problem */}\n      <Section id=\"section-2\" background=\"default\">\n        <div className=\"text-center max-w-4xl mx-auto\">\n          <motion.h2\n            initial={{ opacity: 0, y: 30 }}\n            whileInView={{ opacity: 1, y: 0 }}\n            transition={{ duration: 0.8 }}\n            className=\"text-4xl md:text-5xl font-bold mb-12 text-brand-text\"\n          >\n            The Bali real estate market feels complex, risky, and disconnected.\n          </motion.h2>\n\n          <div className=\"grid md:grid-cols-3 gap-8\">\n            {[\n              {\n                title: \"Legal Confusion\",\n                description: \"Buyers don't understand leasehold laws\",\n                icon: \"⚖️\"\n              },\n              {\n                title: \"Limited Reach\",\n                description: \"Sellers can't reach serious foreign buyers\",\n                icon: \"🌐\"\n              },\n              {\n                title: \"No Trust Source\",\n                description: \"No trusted, centralized source of truth\",\n                icon: \"🔍\"\n              }\n            ].map((problem, index) => (\n              <motion.div\n                key={index}\n                initial={{ opacity: 0, y: 50 }}\n                whileInView={{ opacity: 1, y: 0 }}\n                transition={{ duration: 0.6, delay: index * 0.2 }}\n                className=\"bg-brand-dark/50 border border-brand-accent/20 rounded-xl p-6 hover:border-brand-accent/40 transition-all duration-300\"\n              >\n                <div className=\"text-4xl mb-4\">{problem.icon}</div>\n                <h3 className=\"text-xl font-semibold mb-3 text-brand-accent\">{problem.title}</h3>\n                <p className=\"text-brand-text/70\">{problem.description}</p>\n              </motion.div>\n            ))}\n          </div>\n        </div>\n      </Section>\n\n      {/* Section 3: The 4 Pillars of Empowerment */}\n      <Section id=\"section-3\" background=\"accent\">\n        <div className=\"text-center max-w-6xl mx-auto\">\n          <motion.div\n            initial={{ opacity: 0, y: 30 }}\n            whileInView={{ opacity: 1, y: 0 }}\n            transition={{ duration: 0.8 }}\n            className=\"mb-16\"\n          >\n            <h2 className=\"text-4xl md:text-5xl font-bold mb-6 text-brand-text\">\n              Our Mission: The 4 Pillars of Empowerment\n            </h2>\n            <p className=\"text-xl text-brand-text/80 max-w-3xl mx-auto\">\n              We believe in transforming the Bali real estate market through four fundamental principles\n            </p>\n          </motion.div>\n\n          <div className=\"grid md:grid-cols-2 lg:grid-cols-4 gap-8\">\n            <PillarCard\n              icon=\"🔍\"\n              title=\"Transparency\"\n              description=\"Open, honest, no surprises — especially about contracts, prices, and regulations.\"\n              index={0}\n            />\n            <PillarCard\n              icon=\"📚\"\n              title=\"Knowledge\"\n              description=\"Clear guides and content in your language, so you can actually understand the rules.\"\n              index={1}\n            />\n            <PillarCard\n              icon=\"🔗\"\n              title=\"Connecting\"\n              description=\"Buyers meet real sellers, sellers meet qualified leads, experts are one click away.\"\n              index={2}\n            />\n            <PillarCard\n              icon=\"💪\"\n              title=\"Empowerment\"\n              description=\"With clarity, comes confidence. You feel ready to act — not just scroll.\"\n              index={3}\n            />\n          </div>\n        </div>\n      </Section>\n\n      {/* Placeholder sections for now */}\n      {Array.from({ length: 6 }, (_, i) => (\n        <Section key={i + 4} id={`section-${i + 4}`} background=\"default\">\n          <div className=\"text-center\">\n            <h2 className=\"text-4xl font-bold mb-4 text-brand-text\">\n              Section {i + 4} - Coming Soon\n            </h2>\n            <p className=\"text-brand-text/70\">\n              This section will be built in the next steps\n            </p>\n          </div>\n        </Section>\n      ))}\n    </main>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AAPA;;;;;;;;AASe,SAAS;IACtB,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACrD,MAAM,gBAAgB;IAEtB,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,eAAe;YACnB,MAAM,WAAW,SAAS,gBAAgB,CAAC;YAC3C,MAAM,iBAAiB,OAAO,OAAO,GAAG,OAAO,WAAW,GAAG;YAE7D,SAAS,OAAO,CAAC,CAAC,SAAS;gBACzB,MAAM,UAAU;gBAChB,MAAM,MAAM,QAAQ,SAAS;gBAC7B,MAAM,SAAS,MAAM,QAAQ,YAAY;gBAEzC,IAAI,kBAAkB,OAAO,iBAAiB,QAAQ;oBACpD,kBAAkB,QAAQ;gBAC5B;YACF;QACF;QAEA,OAAO,gBAAgB,CAAC,UAAU;QAClC,OAAO,IAAM,OAAO,mBAAmB,CAAC,UAAU;IACpD,GAAG,EAAE;IAEL,qBACE,8OAAC;QAAK,WAAU;;0BACd,8OAAC,gIAAA,CAAA,UAAU;gBAAC,gBAAgB;gBAAgB,eAAe;;;;;;0BAG3D,8OAAC,6HAAA,CAAA,UAAO;gBAAC,IAAG;gBAAY,YAAW;;kCACjC,8OAAC,yIAAA,CAAA,UAAmB;;;;;kCACpB,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gCACT,SAAS;oCAAE,SAAS;oCAAG,OAAO;gCAAI;gCAClC,SAAS;oCAAE,SAAS;oCAAG,OAAO;gCAAE;gCAChC,YAAY;oCAAE,UAAU;oCAAG,MAAM;gCAAU;gCAC3C,WAAU;;kDAEV,8OAAC,0LAAA,CAAA,SAAM,CAAC,EAAE;wCACR,WAAU;wCACV,SAAS;4CAAE,SAAS;4CAAG,GAAG;wCAAG;wCAC7B,SAAS;4CAAE,SAAS;4CAAG,GAAG;wCAAE;wCAC5B,YAAY;4CAAE,UAAU;4CAAK,OAAO;wCAAI;kDACzC;;;;;;kDAGD,8OAAC,0LAAA,CAAA,SAAM,CAAC,CAAC;wCACP,WAAU;wCACV,SAAS;4CAAE,SAAS;4CAAG,GAAG;wCAAG;wCAC7B,SAAS;4CAAE,SAAS;4CAAG,GAAG;wCAAE;wCAC5B,YAAY;4CAAE,UAAU;4CAAG,OAAO;wCAAI;kDACvC;;;;;;;;;;;;0CAKH,8OAAC,0LAAA,CAAA,SAAM,CAAC,MAAM;gCACZ,SAAS;oCAAE,SAAS;oCAAG,GAAG;gCAAG;gCAC7B,SAAS;oCAAE,SAAS;oCAAG,GAAG;gCAAE;gCAC5B,YAAY;oCAAE,UAAU;oCAAK,OAAO;gCAAI;gCACxC,YAAY;oCAAE,OAAO;gCAAK;gCAC1B,UAAU;oCAAE,OAAO;gCAAK;gCACxB,SAAS,IAAM,SAAS,cAAc,CAAC,cAAc,eAAe;wCAAE,UAAU;oCAAS;gCACzF,WAAU;;kDAEV,8OAAC;wCAAK,WAAU;;4CAAgB;0DAE9B,8OAAC,0LAAA,CAAA,SAAM,CAAC,IAAI;gDACV,SAAS;oDAAE,GAAG;wDAAC;wDAAG;wDAAG;qDAAE;gDAAC;gDACxB,YAAY;oDAAE,UAAU;oDAAK,QAAQ;gDAAS;gDAC9C,WAAU;0DACX;;;;;;;;;;;;kDAIH,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wCACT,WAAU;wCACV,YAAY;4CAAE,OAAO;wCAAI;wCACzB,YAAY;4CAAE,UAAU;wCAAI;;;;;;;;;;;;;;;;;;;;;;;;0BAOpC,8OAAC,6HAAA,CAAA,UAAO;gBAAC,IAAG;gBAAY,YAAW;0BACjC,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC,0LAAA,CAAA,SAAM,CAAC,EAAE;4BACR,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAG;4BAC7B,aAAa;gCAAE,SAAS;gCAAG,GAAG;4BAAE;4BAChC,YAAY;gCAAE,UAAU;4BAAI;4BAC5B,WAAU;sCACX;;;;;;sCAID,8OAAC;4BAAI,WAAU;sCACZ;gCACC;oCACE,OAAO;oCACP,aAAa;oCACb,MAAM;gCACR;gCACA;oCACE,OAAO;oCACP,aAAa;oCACb,MAAM;gCACR;gCACA;oCACE,OAAO;oCACP,aAAa;oCACb,MAAM;gCACR;6BACD,CAAC,GAAG,CAAC,CAAC,SAAS,sBACd,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oCAET,SAAS;wCAAE,SAAS;wCAAG,GAAG;oCAAG;oCAC7B,aAAa;wCAAE,SAAS;wCAAG,GAAG;oCAAE;oCAChC,YAAY;wCAAE,UAAU;wCAAK,OAAO,QAAQ;oCAAI;oCAChD,WAAU;;sDAEV,8OAAC;4CAAI,WAAU;sDAAiB,QAAQ,IAAI;;;;;;sDAC5C,8OAAC;4CAAG,WAAU;sDAAgD,QAAQ,KAAK;;;;;;sDAC3E,8OAAC;4CAAE,WAAU;sDAAsB,QAAQ,WAAW;;;;;;;mCARjD;;;;;;;;;;;;;;;;;;;;;0BAgBf,8OAAC,6HAAA,CAAA,UAAO;gBAAC,IAAG;gBAAY,YAAW;0BACjC,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4BACT,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAG;4BAC7B,aAAa;gCAAE,SAAS;gCAAG,GAAG;4BAAE;4BAChC,YAAY;gCAAE,UAAU;4BAAI;4BAC5B,WAAU;;8CAEV,8OAAC;oCAAG,WAAU;8CAAsD;;;;;;8CAGpE,8OAAC;oCAAE,WAAU;8CAA+C;;;;;;;;;;;;sCAK9D,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,gIAAA,CAAA,UAAU;oCACT,MAAK;oCACL,OAAM;oCACN,aAAY;oCACZ,OAAO;;;;;;8CAET,8OAAC,gIAAA,CAAA,UAAU;oCACT,MAAK;oCACL,OAAM;oCACN,aAAY;oCACZ,OAAO;;;;;;8CAET,8OAAC,gIAAA,CAAA,UAAU;oCACT,MAAK;oCACL,OAAM;oCACN,aAAY;oCACZ,OAAO;;;;;;8CAET,8OAAC,gIAAA,CAAA,UAAU;oCACT,MAAK;oCACL,OAAM;oCACN,aAAY;oCACZ,OAAO;;;;;;;;;;;;;;;;;;;;;;;YAOd,MAAM,IAAI,CAAC;gBAAE,QAAQ;YAAE,GAAG,CAAC,GAAG,kBAC7B,8OAAC,6HAAA,CAAA,UAAO;oBAAa,IAAI,CAAC,QAAQ,EAAE,IAAI,GAAG;oBAAE,YAAW;8BACtD,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAU;;oCAA0C;oCAC7C,IAAI;oCAAE;;;;;;;0CAEjB,8OAAC;gCAAE,WAAU;0CAAqB;;;;;;;;;;;;mBALxB,IAAI;;;;;;;;;;;AAa1B", "debugId": null}}]}