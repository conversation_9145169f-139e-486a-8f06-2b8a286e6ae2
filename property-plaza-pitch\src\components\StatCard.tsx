'use client';

import { motion } from 'framer-motion';
import { useEffect, useState } from 'react';

interface StatCardProps {
  number: string;
  label: string;
  description: string;
  index: number;
  icon?: string;
}

export default function StatCard({ number, label, description, index, icon }: StatCardProps) {
  const [isVisible, setIsVisible] = useState(false);

  useEffect(() => {
    const timer = setTimeout(() => setIsVisible(true), index * 200);
    return () => clearTimeout(timer);
  }, [index]);

  return (
    <motion.div
      initial={{ opacity: 0, y: 50, scale: 0.8 }}
      whileInView={{ opacity: 1, y: 0, scale: 1 }}
      transition={{ 
        duration: 0.6, 
        delay: index * 0.1,
        ease: 'easeOut'
      }}
      whileHover={{ 
        scale: 1.05,
        y: -5,
        transition: { duration: 0.3 }
      }}
      className="group relative bg-brand-secondary/30 border border-brand-accent/10 rounded-2xl p-8 hover:border-brand-accent/30 transition-all duration-500 backdrop-blur-sm"
    >
      {/* Glow effect */}
      <div className="absolute inset-0 rounded-2xl bg-gradient-to-br from-brand-accent/5 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-500" />

      <div className="relative z-10 text-center">
        {/* Icon */}
        {icon && (
          <div className="w-16 h-16 bg-brand-accent/20 rounded-full flex items-center justify-center mb-6 mx-auto group-hover:bg-brand-accent/30 transition-colors duration-300">
            <div className="w-8 h-8 bg-brand-accent rounded-full"></div>
          </div>
        )}

        {/* Number with counting animation */}
        <motion.div
          initial={{ scale: 0.5, opacity: 0 }}
          whileInView={{ scale: 1, opacity: 1 }}
          transition={{ duration: 0.8, delay: index * 0.1 + 0.3 }}
          className="text-4xl md:text-5xl font-bold text-brand-accent mb-4"
        >
          {isVisible && (
            <CountingNumber
              target={parseInt(number.replace(/\D/g, ''))}
              suffix={number.replace(/\d/g, '')}
            />
          )}
        </motion.div>

        {/* Label */}
        <motion.h3
          initial={{ opacity: 0 }}
          whileInView={{ opacity: 1 }}
          transition={{ duration: 0.6, delay: index * 0.1 + 0.4 }}
          className="text-lg font-semibold text-brand-text mb-3"
        >
          {label}
        </motion.h3>

        {/* Description */}
        <motion.p
          initial={{ opacity: 0 }}
          whileInView={{ opacity: 1 }}
          transition={{ duration: 0.6, delay: index * 0.1 + 0.5 }}
          className="text-sm text-brand-text/60 leading-relaxed"
        >
          {description}
        </motion.p>
      </div>
    </motion.div>
  );
}

// Helper component for counting animation
function CountingNumber({ target, suffix }: { target: number; suffix: string }) {
  const [count, setCount] = useState(0);

  useEffect(() => {
    const duration = 2000; // 2 seconds
    const steps = 60;
    const increment = target / steps;
    let current = 0;

    const timer = setInterval(() => {
      current += increment;
      if (current >= target) {
        setCount(target);
        clearInterval(timer);
      } else {
        setCount(Math.floor(current));
      }
    }, duration / steps);

    return () => clearInterval(timer);
  }, [target]);

  return <span>{count}{suffix}</span>;
}
