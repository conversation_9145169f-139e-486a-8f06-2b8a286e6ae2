'use client';

import { useState, useEffect } from 'react';

interface NavigationProps {
  currentSection: number;
  totalSections: number;
}

export default function Navigation({ currentSection, totalSections }: NavigationProps) {
  const [isVisible, setIsVisible] = useState(false);

  useEffect(() => {
    const timer = setTimeout(() => setIsVisible(true), 1000);
    return () => clearTimeout(timer);
  }, []);

  const scrollToSection = (sectionIndex: number) => {
    const section = document.getElementById(`section-${sectionIndex}`);
    if (section) {
      section.scrollIntoView({ behavior: 'smooth' });
    }
  };

  if (!isVisible) return null;

  return (
    <nav className="fixed top-6 right-6 z-50 bg-brand-dark/80 backdrop-blur-sm rounded-full px-4 py-2 border border-brand-accent/20">
      <div className="flex items-center space-x-2">
        <span className="text-sm text-brand-text/70">
          {currentSection} of {totalSections}
        </span>
        <div className="flex space-x-1">
          {Array.from({ length: totalSections }, (_, i) => (
            <button
              key={i}
              onClick={() => scrollToSection(i + 1)}
              className={`w-2 h-2 rounded-full transition-all duration-300 ${
                i + 1 === currentSection
                  ? 'bg-brand-accent scale-125'
                  : 'bg-brand-text/30 hover:bg-brand-text/50'
              }`}
              aria-label={`Go to section ${i + 1}`}
            />
          ))}
        </div>
      </div>
    </nav>
  );
}
