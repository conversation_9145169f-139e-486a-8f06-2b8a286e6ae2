'use client';

import { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import Navigation from '@/components/Navigation';
import Section from '@/components/Section';
import PillarCard from '@/components/PillarCard';
import BackgroundAnimation from '@/components/BackgroundAnimation';
import StatCard from '@/components/StatCard';
import LogoMerge from '@/components/LogoMerge';

export default function Home() {
  const [currentSection, setCurrentSection] = useState(1);
  const totalSections = 9;

  useEffect(() => {
    const handleScroll = () => {
      const sections = document.querySelectorAll('.section');
      const scrollPosition = window.scrollY + window.innerHeight / 2;

      sections.forEach((section, index) => {
        const element = section as HTMLElement;
        const top = element.offsetTop;
        const bottom = top + element.offsetHeight;

        if (scrollPosition >= top && scrollPosition < bottom) {
          setCurrentSection(index + 1);
        }
      });
    };

    window.addEventListener('scroll', handleScroll);
    return () => window.removeEventListener('scroll', handleScroll);
  }, []);

  return (
    <main className="relative">
      <Navigation currentSection={currentSection} totalSections={totalSections} />

      {/* Section 1: Hero */}
      <Section id="section-1" background="gradient">
        <BackgroundAnimation />
        <div className="text-center relative z-10">
          <motion.div
            initial={{ opacity: 0, scale: 0.8 }}
            animate={{ opacity: 1, scale: 1 }}
            transition={{ duration: 1, ease: 'easeOut' }}
            className="mb-8"
          >
            <motion.h1
              className="text-5xl md:text-7xl font-bold mb-6 bg-gradient-to-r from-brand-text to-brand-accent bg-clip-text text-transparent"
              initial={{ opacity: 0, y: 30 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 1.2, delay: 0.2 }}
            >
              Empower Property Decisions in Bali
            </motion.h1>
            <motion.p
              className="text-xl md:text-2xl text-brand-text/70 mb-8 max-w-3xl mx-auto"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 1, delay: 0.4 }}
            >
              Through transparency, knowledge, connection and trust.
            </motion.p>
          </motion.div>

          <motion.button
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 0.6 }}
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
            onClick={() => document.getElementById('section-2')?.scrollIntoView({ behavior: 'smooth' })}
            className="group relative bg-brand-accent hover:bg-brand-accent/90 text-white px-8 py-4 rounded-full text-lg font-semibold transition-all duration-300 transform hover:shadow-lg hover:shadow-brand-accent/25 overflow-hidden"
          >
            <span className="relative z-10">
              Start the Experience
              <motion.span
                animate={{ y: [0, 5, 0] }}
                transition={{ duration: 1.5, repeat: Infinity }}
                className="ml-2 inline-block"
              >
                ↓
              </motion.span>
            </span>
            <motion.div
              className="absolute inset-0 bg-gradient-to-r from-brand-accent to-brand-accent/80"
              whileHover={{ scale: 1.1 }}
              transition={{ duration: 0.3 }}
            />
          </motion.button>
        </div>
      </Section>

      {/* Section 2: The Problem */}
      <Section id="section-2" background="default">
        <div className="text-center max-w-5xl mx-auto">
          <motion.h2
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            className="text-4xl md:text-5xl font-bold mb-4 text-brand-text"
          >
            The Bali real estate market feels
          </motion.h2>
          <motion.div
            initial={{ opacity: 0, scale: 0.8 }}
            whileInView={{ opacity: 1, scale: 1 }}
            transition={{ duration: 0.8, delay: 0.2 }}
            className="mb-16"
          >
            <span className="text-4xl md:text-5xl font-bold text-red-400">
              complex, risky, and disconnected.
            </span>
          </motion.div>

          <div className="grid md:grid-cols-3 gap-10">
            {[
              {
                title: "Legal Confusion",
                description: "Buyers don't understand leasehold laws",
                color: "from-red-500/10 to-red-600/5"
              },
              {
                title: "Limited Reach",
                description: "Sellers can't reach serious foreign buyers",
                color: "from-orange-500/10 to-orange-600/5"
              },
              {
                title: "No Trust Source",
                description: "No trusted, centralized source of truth",
                color: "from-yellow-500/10 to-yellow-600/5"
              }
            ].map((problem, index) => (
              <motion.div
                key={index}
                initial={{ opacity: 0, y: 50, rotateX: -15 }}
                whileInView={{ opacity: 1, y: 0, rotateX: 0 }}
                transition={{ duration: 0.6, delay: index * 0.2 }}
                whileHover={{
                  y: -10,
                  scale: 1.02,
                  transition: { duration: 0.3 }
                }}
                className={`relative bg-gradient-to-br ${problem.color} backdrop-blur-sm border border-brand-accent/10 rounded-xl p-10 hover:border-brand-accent/30 transition-all duration-300 group`}
              >
                <div className="absolute inset-0 bg-brand-secondary/30 rounded-xl" />
                <div className="relative z-10">
                  <div className="w-16 h-16 bg-brand-accent/20 rounded-full flex items-center justify-center mb-8 mx-auto">
                    <div className="w-8 h-8 bg-brand-accent rounded-full"></div>
                  </div>
                  <h3 className="text-xl font-semibold mb-6 text-brand-accent group-hover:text-brand-accent transition-colors duration-300 text-center">
                    {problem.title}
                  </h3>
                  <p className="text-brand-text/60 group-hover:text-brand-text/80 transition-colors duration-300 leading-relaxed text-center">
                    {problem.description}
                  </p>
                </div>
              </motion.div>
            ))}
          </div>
        </div>
      </Section>

      {/* Section 3: The 4 Pillars of Empowerment */}
      <Section id="section-3" background="accent">
        <div className="text-center max-w-6xl mx-auto">
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            className="mb-16"
          >
            <h2 className="text-4xl md:text-5xl font-bold mb-6 text-brand-text">
              Our Mission: The 4 Pillars of Empowerment
            </h2>
            <p className="text-xl text-brand-text/70 max-w-3xl mx-auto">
              We believe in transforming the Bali real estate market through four fundamental principles
            </p>
          </motion.div>

          <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-8">
            <PillarCard
              icon="🔍"
              title="Transparency"
              description="Open, honest, no surprises — especially about contracts, prices, and regulations."
              index={0}
            />
            <PillarCard
              icon="📚"
              title="Knowledge"
              description="Clear guides and content in your language, so you can actually understand the rules."
              index={1}
            />
            <PillarCard
              icon="🔗"
              title="Connecting"
              description="Buyers meet real sellers, sellers meet qualified leads, experts are one click away."
              index={2}
            />
            <PillarCard
              icon="💪"
              title="Empowerment"
              description="With clarity, comes confidence. You feel ready to act — not just scroll."
              index={3}
            />
          </div>
        </div>
      </Section>

      {/* Section 4: Who We Are - Property Plaza */}
      <Section id="section-4" background="default">
        <div className="max-w-6xl mx-auto">
          <div className="text-center mb-16">
            <motion.h2
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8 }}
              className="text-4xl md:text-5xl font-bold mb-6 text-brand-text"
            >
              Who We Are – Property Plaza
            </motion.h2>
            <motion.p
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8, delay: 0.2 }}
              className="text-xl text-brand-text/70 max-w-3xl mx-auto"
            >
              A fresh approach to Bali real estate, built on trust and transparency
            </motion.p>
          </div>

          {/* Statistics Grid */}
          <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-6 mb-16">
            <StatCard
              number="30+"
              label="Active Listings"
              description="Carefully curated properties across Bali"
              index={0}
              icon="🏡"
            />
            <StatCard
              number="350+"
              label="Early Users"
              description="Growing community of buyers and sellers"
              index={1}
              icon="👥"
            />
            <StatCard
              number="30"
              label="Days Live"
              description="Fresh platform with proven traction"
              index={2}
              icon="🚀"
            />
            <StatCard
              number="3"
              label="Languages"
              description="Multilingual, content-focused approach"
              index={3}
              icon="🌍"
            />
          </div>

          {/* Key Features */}
          <div className="grid md:grid-cols-2 gap-12 items-center">
            <motion.div
              initial={{ opacity: 0, x: -50 }}
              whileInView={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.8 }}
            >
              <h3 className="text-2xl font-bold text-brand-accent mb-6">
                What Makes Us Different
              </h3>
              <div className="space-y-4">
                {[
                  "Multilingual content in your language",
                  "Smart AI-powered follow-up system",
                  "Transparent pricing and legal guidance",
                  "Direct connection between buyers and sellers"
                ].map((feature, index) => (
                  <motion.div
                    key={index}
                    initial={{ opacity: 0, x: -20 }}
                    whileInView={{ opacity: 1, x: 0 }}
                    transition={{ duration: 0.6, delay: index * 0.1 }}
                    className="flex items-center space-x-3"
                  >
                    <div className="w-2 h-2 bg-brand-accent rounded-full" />
                    <span className="text-brand-text/70">{feature}</span>
                  </motion.div>
                ))}
              </div>
            </motion.div>

            <motion.div
              initial={{ opacity: 0, x: 50 }}
              whileInView={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.8 }}
              className="relative"
            >
              <div className="bg-gradient-to-br from-brand-accent/10 to-brand-accent/5 rounded-2xl p-8 border border-brand-accent/20">
                <div className="text-center">
                  <div className="text-6xl mb-4">📱</div>
                  <h4 className="text-xl font-semibold text-brand-text mb-2">
                    Modern Platform
                  </h4>
                  <p className="text-brand-text/60">
                    Built with the latest technology for the best user experience
                  </p>
                </div>
              </div>
            </motion.div>
          </div>
        </div>
      </Section>

      {/* Section 5: Why Paradise Indonesia */}
      <Section id="section-5" background="accent">
        <div className="max-w-6xl mx-auto">
          <div className="text-center mb-20">
            <motion.h2
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8 }}
              className="text-4xl md:text-5xl font-bold mb-8 text-brand-text"
            >
              Why Paradise Indonesia
            </motion.h2>
            <motion.p
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8, delay: 0.2 }}
              className="text-xl text-brand-text/70 max-w-3xl mx-auto"
            >
              The perfect partner to bring trust and expertise to Bali real estate
            </motion.p>
          </div>

          <div className="grid lg:grid-cols-2 gap-20 items-start">
            {/* Left side - Strengths */}
            <motion.div
              initial={{ opacity: 0, x: -50 }}
              whileInView={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.8 }}
            >
              <h3 className="text-2xl font-bold text-brand-accent mb-12">
                Your Strengths
              </h3>

              <div className="space-y-10">
                {[
                  {
                    title: "Legal Knowledge",
                    description: "Deep understanding of Indonesian property law and regulations"
                  },
                  {
                    title: "Ground Presence in Bali",
                    description: "Local expertise and established network across the island"
                  },
                  {
                    title: "Existing Trust",
                    description: "Proven relationships with sellers, developers, and partners"
                  }
                ].map((strength, index) => (
                  <motion.div
                    key={index}
                    initial={{ opacity: 0, y: 30 }}
                    whileInView={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.6, delay: index * 0.2 }}
                    className="flex items-start space-x-6 p-6 rounded-xl bg-brand-dark/20 border border-brand-accent/10 hover:border-brand-accent/30 transition-all duration-300"
                  >
                    <div className="flex-shrink-0 w-12 h-12 bg-brand-secondary rounded-lg flex items-center justify-center">
                      <div className="w-6 h-6 bg-brand-accent rounded-full"></div>
                    </div>
                    <div className="flex-1">
                      <h4 className="text-xl font-semibold text-brand-text mb-3">
                        {strength.title}
                      </h4>
                      <p className="text-brand-text/60 leading-relaxed">
                        {strength.description}
                      </p>
                    </div>
                  </motion.div>
                ))}
              </div>
            </motion.div>

            {/* Right side - Quote and Visual */}
            <motion.div
              initial={{ opacity: 0, x: 50 }}
              whileInView={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.8 }}
              className="relative"
            >
              {/* Quote Card */}
              <div className="relative bg-brand-secondary/50 rounded-3xl p-10 border border-brand-accent/20 backdrop-blur-sm">
                <div className="text-center mb-8">
                  <div className="inline-flex items-center justify-center w-20 h-20 bg-brand-accent rounded-full mb-6">
                    <span className="text-2xl font-bold text-white">PP</span>
                  </div>
                  <h4 className="text-xl font-semibold text-brand-accent mb-3">Property Plaza</h4>
                  <p className="text-sm text-brand-text/50">Partnership Vision</p>
                </div>

                <div className="text-center">
                  <blockquote className="text-xl font-medium text-brand-text mb-8 italic leading-relaxed">
                    "You bring the reputation. We bring the distribution."
                  </blockquote>

                  <div className="flex items-center justify-center space-x-8 text-sm text-brand-text/60">
                    <div className="flex items-center space-x-3">
                      <div className="w-8 h-8 bg-brand-dark/50 rounded-full flex items-center justify-center">
                        <div className="w-3 h-3 bg-brand-accent rounded-full"></div>
                      </div>
                      <span>Local Expertise</span>
                    </div>
                    <span className="text-brand-accent font-bold">+</span>
                    <div className="flex items-center space-x-3">
                      <div className="w-8 h-8 bg-brand-dark/50 rounded-full flex items-center justify-center">
                        <div className="w-3 h-3 bg-brand-accent rounded-full"></div>
                      </div>
                      <span>Global Reach</span>
                    </div>
                  </div>
                </div>
              </div>
            </motion.div>
          </div>
        </div>
      </Section>

      {/* Section 6: Together We're Stronger */}
      <Section id="section-6" background="default">
        <div className="max-w-6xl mx-auto text-center">
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            className="mb-8"
          >
            <h2 className="text-4xl md:text-5xl font-bold mb-6 text-brand-text">
              Together We're Stronger
            </h2>
            <p className="text-xl text-brand-text/80 max-w-3xl mx-auto mb-8">
              This isn't just promotion. It's infrastructure.
            </p>
          </motion.div>

          {/* Logo Merge Animation */}
          <LogoMerge />

          {/* Key Message */}
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 0.5 }}
            className="mt-16"
          >
            <div className="bg-gradient-to-br from-brand-accent/10 to-brand-accent/5 rounded-3xl p-12 border border-brand-accent/20">
              <blockquote className="text-3xl md:text-4xl font-bold text-brand-text mb-8 italic">
                "Together, we become the go-to destination for trusted real estate in Bali."
              </blockquote>

              <div className="grid md:grid-cols-2 gap-8 mt-12">
                <motion.div
                  initial={{ opacity: 0, x: -30 }}
                  whileInView={{ opacity: 1, x: 0 }}
                  transition={{ duration: 0.6, delay: 0.2 }}
                  className="text-left"
                >
                  <h3 className="text-xl font-semibold text-brand-accent mb-4">
                    What You Bring
                  </h3>
                  <ul className="space-y-2 text-brand-text/80">
                    <li>• Local market expertise</li>
                    <li>• Legal knowledge & compliance</li>
                    <li>• Established seller relationships</li>
                    <li>• Ground presence in Bali</li>
                  </ul>
                </motion.div>

                <motion.div
                  initial={{ opacity: 0, x: 30 }}
                  whileInView={{ opacity: 1, x: 0 }}
                  transition={{ duration: 0.6, delay: 0.4 }}
                  className="text-left"
                >
                  <h3 className="text-xl font-semibold text-brand-accent mb-4">
                    What We Bring
                  </h3>
                  <ul className="space-y-2 text-brand-text/80">
                    <li>• Global digital reach</li>
                    <li>• Modern platform technology</li>
                    <li>• Multilingual content strategy</li>
                    <li>• AI-powered lead management</li>
                  </ul>
                </motion.div>
              </div>
            </div>
          </motion.div>
        </div>
      </Section>

      {/* Placeholder sections for now */}
      {Array.from({ length: 3 }, (_, i) => (
        <Section key={i + 7} id={`section-${i + 7}`} background="default">
          <div className="text-center">
            <h2 className="text-4xl font-bold mb-4 text-brand-text">
              Section {i + 7} - Coming Soon
            </h2>
            <p className="text-brand-text/70">
              This section will be built in the next steps
            </p>
          </div>
        </Section>
      ))}
    </main>
  );
}
