'use client';

import { motion } from 'framer-motion';

interface PillarCardProps {
  icon: string;
  title: string;
  description: string;
  index: number;
}

export default function PillarCard({ icon, title, description, index }: PillarCardProps) {
  return (
    <motion.div
      initial={{ opacity: 0, y: 50, scale: 0.9 }}
      whileInView={{ opacity: 1, y: 0, scale: 1 }}
      transition={{ 
        duration: 0.6, 
        delay: index * 0.15,
        ease: 'easeOut'
      }}
      whileHover={{ 
        scale: 1.05,
        y: -10,
        transition: { duration: 0.3 }
      }}
      className="group relative bg-gradient-to-br from-brand-dark/80 to-brand-dark/40 border border-brand-accent/20 rounded-2xl p-8 hover:border-brand-accent/60 transition-all duration-500 backdrop-blur-sm"
    >
      {/* Glow effect on hover */}
      <div className="absolute inset-0 rounded-2xl bg-gradient-to-br from-brand-accent/10 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-500" />
      
      <div className="relative z-10">
        {/* Icon */}
        <motion.div
          whileHover={{ rotate: 360 }}
          transition={{ duration: 0.6 }}
          className="text-6xl mb-6 text-center"
        >
          {icon}
        </motion.div>
        
        {/* Title */}
        <h3 className="text-2xl font-bold mb-4 text-brand-accent text-center group-hover:text-brand-accent/90 transition-colors duration-300">
          {title}
        </h3>
        
        {/* Description */}
        <p className="text-brand-text/80 text-center leading-relaxed group-hover:text-brand-text transition-colors duration-300">
          {description}
        </p>
        
        {/* Decorative line */}
        <motion.div
          initial={{ width: 0 }}
          whileInView={{ width: '100%' }}
          transition={{ duration: 0.8, delay: index * 0.15 + 0.3 }}
          className="h-0.5 bg-gradient-to-r from-transparent via-brand-accent to-transparent mt-6"
        />
      </div>
    </motion.div>
  );
}
