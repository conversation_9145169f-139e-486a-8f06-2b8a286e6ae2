'use client';

import { motion } from 'framer-motion';

interface PillarCardProps {
  icon: string;
  title: string;
  description: string;
  index: number;
}

export default function PillarCard({ icon, title, description, index }: PillarCardProps) {
  return (
    <motion.div
      initial={{ opacity: 0, y: 50, scale: 0.9 }}
      whileInView={{ opacity: 1, y: 0, scale: 1 }}
      transition={{ 
        duration: 0.6, 
        delay: index * 0.15,
        ease: 'easeOut'
      }}
      whileHover={{ 
        scale: 1.05,
        y: -10,
        transition: { duration: 0.3 }
      }}
      className="group relative bg-brand-secondary/30 border border-brand-accent/10 rounded-2xl p-10 hover:border-brand-accent/30 transition-all duration-500 backdrop-blur-sm"
    >
      {/* Glow effect on hover */}
      <div className="absolute inset-0 rounded-2xl bg-gradient-to-br from-brand-accent/5 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-500" />

      <div className="relative z-10">
        {/* Icon Container */}
        <div className="w-20 h-20 bg-brand-accent/20 rounded-full flex items-center justify-center mb-8 mx-auto group-hover:bg-brand-accent/30 transition-colors duration-300">
          <div className="w-10 h-10 bg-brand-accent rounded-full"></div>
        </div>

        {/* Title */}
        <h3 className="text-2xl font-bold mb-6 text-brand-accent text-center group-hover:text-brand-accent transition-colors duration-300">
          {title}
        </h3>

        {/* Description */}
        <p className="text-brand-text/60 text-center leading-relaxed group-hover:text-brand-text/80 transition-colors duration-300">
          {description}
        </p>

        {/* Decorative line */}
        <motion.div
          initial={{ width: 0 }}
          whileInView={{ width: '60%' }}
          transition={{ duration: 0.8, delay: index * 0.15 + 0.3 }}
          className="h-0.5 bg-brand-accent mt-8 mx-auto"
        />
      </div>
    </motion.div>
  );
}
