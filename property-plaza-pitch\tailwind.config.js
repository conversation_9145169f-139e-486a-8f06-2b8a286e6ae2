/** @type {import('tailwindcss').Config} */
module.exports = {
  content: [
    './src/pages/**/*.{js,ts,jsx,tsx,mdx}',
    './src/components/**/*.{js,ts,jsx,tsx,mdx}',
    './src/app/**/*.{js,ts,jsx,tsx,mdx}',
  ],
  theme: {
    extend: {
      colors: {
        // Property Plaza brand colors
        'brand-dark': '#1a1a2e',     // Property Plaza dark background
        'brand-accent': '#00d4aa',   // Property Plaza green accent
        'brand-text': '#ffffff',     // Pure white text
        'brand-secondary': '#16213e', // Darker blue for cards
        'brand-muted': '#a0a0a0',    // Muted text color
      },
      fontFamily: {
        sans: ['Inter', 'Plus Jakarta Sans', 'sans-serif'],
      },
      animation: {
        'fade-in': 'fadeIn 0.6s ease-out',
        'slide-up': 'slideUp 0.8s ease-out',
        'float': 'float 3s ease-in-out infinite',
      },
      keyframes: {
        fadeIn: {
          '0%': { opacity: '0' },
          '100%': { opacity: '1' },
        },
        slideUp: {
          '0%': { opacity: '0', transform: 'translateY(20px)' },
          '100%': { opacity: '1', transform: 'translateY(0)' },
        },
        float: {
          '0%, 100%': { transform: 'translateY(0px)' },
          '50%': { transform: 'translateY(-10px)' },
        },
      },
      scrollSnapType: {
        y: 'y mandatory',
      },
      scrollSnapAlign: {
        start: 'start',
      },
    },
  },
  plugins: [],
}
